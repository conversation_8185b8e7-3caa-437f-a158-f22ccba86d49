<?php

namespace App\Services\Shipping;

use App\Models\Order;
use App\Models\ShippingPartner;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ShiprocketService extends AbstractShippingService
{
    protected $baseUrl = 'https://apiv2.shiprocket.in/v1/external';
    protected $authToken = null;

    public function __construct(ShippingPartner $shippingPartner = null)
    {
        if (!$shippingPartner) {
            $shippingPartner = ShippingPartner::where('code', 'shiprocket')->first();
        }

        parent::__construct($shippingPartner);
    }

    /**
     * Get authentication token from Shiprocket
     */
    protected function getAuthToken()
    {
        if ($this->authToken) {
            return $this->authToken;
        }

        try {
            Log::info('Shiprocket Auth Attempt', [
                'email' => $this->apiKey,
                'url' => $this->baseUrl . '/auth/login'
            ]);

            $response = Http::post($this->baseUrl . '/auth/login', [
                'email' => $this->apiKey,
                'password' => $this->apiSecret
            ]);

            Log::info('Shiprocket Auth Response', [
                'status' => $response->status(),
                'body' => $response->body()
            ]);

            if ($response->successful()) {
                $data = $response->json();
                if (isset($data['token'])) {
                    $this->authToken = $data['token'];
                    Log::info('Shiprocket Auth Success', ['token_length' => strlen($this->authToken)]);
                    return $this->authToken;
                }
            }

            Log::error('Shiprocket Auth Failed: ' . $response->body());
        } catch (\Exception $e) {
            Log::error('Shiprocket Auth Error: ' . $e->getMessage());
        }

        return null;
    }

    /**
     * Create or get pickup location in Shiprocket
     *
     * @param array $address
     * @return string|null
     */
    protected function getOrCreatePickupLocation(array $address): ?string
    {
        $token = $this->getAuthToken();
        if (!$token) {
            Log::error('Shiprocket: No auth token available for pickup location creation');
            return null;
        }

        // Validate required address fields
        $requiredFields = ['name', 'address', 'city', 'state', 'country', 'postal_code', 'phone'];
        foreach ($requiredFields as $field) {
            if (empty($address[$field])) {
                Log::error("Shiprocket: Missing required address field: {$field}", $address);
                return null;
            }
        }

        Log::info('Shiprocket: Getting existing pickup locations');

        // First, try to get existing pickup locations
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $token,
                'Content-Type' => 'application/json'
            ])->get($this->baseUrl . '/settings/company/pickup');

            Log::info('Shiprocket Get Pickup Locations Response', [
                'status' => $response->status(),
                'body' => $response->body()
            ]);

            if ($response->successful()) {
                $data = $response->json();
                if (isset($data['data']['shipping_address']) && is_array($data['data']['shipping_address'])) {
                    foreach ($data['data']['shipping_address'] as $location) {
                        // Check if we have a matching location by postal code and name
                        if (isset($location['pin_code']) && isset($location['pickup_location']) &&
                            $location['pin_code'] == $address['postal_code'] &&
                            $location['pickup_location'] == $address['name']) {
                            Log::info('Shiprocket: Found existing pickup location', ['location' => $location['pickup_location']]);
                            return $location['pickup_location'];
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            Log::error('Shiprocket Get Pickup Locations Error: ' . $e->getMessage());
        }

        // If no matching location found, create a new one
        Log::info('Shiprocket: Creating new pickup location', ['address' => $address]);

        try {
            // Format phone number using the standardized method
            $cleanPhone = $this->formatPhoneNumber($address['phone']);

            $pickupData = [
                'pickup_location' => $address['name'],
                'name' => $address['name'],
                'email' => $this->apiKey, // Use the same email as login
                'phone' => $cleanPhone,
                'address' => $address['address'],
                'address_2' => '',
                'city' => $address['city'],
                'state' => $address['state'],
                'country' => $address['country'],
                'pin_code' => $address['postal_code']
            ];

            Log::info('Shiprocket Create Pickup Location Request', [
                'url' => $this->baseUrl . '/settings/company/addpickup',
                'data' => $pickupData
            ]);

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $token,
                'Content-Type' => 'application/json'
            ])->post($this->baseUrl . '/settings/company/addpickup', $pickupData);

            Log::info('Shiprocket Create Pickup Location Response', [
                'status' => $response->status(),
                'body' => $response->body()
            ]);

            if ($response->successful()) {
                $data = $response->json();

                // Check for success in response
                if (isset($data['success']) && $data['success'] === true) {
                    $locationName = $data['pickup_location'] ?? $address['name'];
                    Log::info('Shiprocket: Successfully created pickup location', ['location' => $locationName]);
                    return $locationName;
                }

                // If response has pickup_location field
                if (isset($data['pickup_location'])) {
                    Log::info('Shiprocket: Pickup location created', ['location' => $data['pickup_location']]);
                    return $data['pickup_location'];
                }

                // Check for error messages in response
                if (isset($data['message'])) {
                    Log::error('Shiprocket: Pickup location creation failed', ['message' => $data['message']]);
                    return null;
                }

                // Sometimes the response doesn't include pickup_location, use the name we sent
                Log::info('Shiprocket: Using fallback pickup location name', ['location' => $address['name']]);
                return $address['name'];
            } else {
                // Handle specific HTTP error codes
                $data = $response->json();
                $errorMessage = $data['message'] ?? 'Unknown error';

                if ($response->status() === 422) {
                    Log::error('Shiprocket: Validation error for pickup location', [
                        'status' => $response->status(),
                        'error' => $errorMessage,
                        'data' => $pickupData
                    ]);
                } else {
                    Log::error('Shiprocket: HTTP error creating pickup location', [
                        'status' => $response->status(),
                        'error' => $errorMessage
                    ]);
                }
            }

            Log::error('Shiprocket Create Pickup Location Failed', [
                'status' => $response->status(),
                'body' => $response->body()
            ]);

        } catch (\Exception $e) {
            Log::error('Shiprocket Create Pickup Location Error: ' . $e->getMessage(), [
                'address' => $address
            ]);
        }

        // Return null to indicate failure - don't use fallback name if creation failed
        return null;
    }

    /**
     * Calculate shipping rate for an order
     *
     * @param Order $order
     * @param array $fromAddress
     * @param array $toAddress
     * @return float
     */
    public function calculateRate(Order $order, array $fromAddress, array $toAddress): float
    {
        $token = $this->getAuthToken();
        if (!$token) {
            return 150.00; // Fallback rate
        }

        $weight = 0;
        $dimensions = [
            'length' => 0,
            'width' => 0,
            'height' => 0
        ];

        // Calculate total weight and dimensions
        foreach ($order->orderDetails as $orderDetail) {
            $product = $orderDetail->product;
            $weight += ($product->weight ?? 0.5) * $orderDetail->quantity;

            // Add dimensions (simplified)
            $dimensions['length'] += ($product->length ?? 10);
            $dimensions['width'] += ($product->width ?? 10);
            $dimensions['height'] += ($product->height ?? 5);
        }

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $token,
                'Content-Type' => 'application/json'
            ])->get($this->baseUrl . '/courier/serviceability', [
                'pickup_postcode' => $fromAddress['postal_code'],
                'delivery_postcode' => $toAddress['postal_code'],
                'weight' => $weight,
                'length' => $dimensions['length'],
                'breadth' => $dimensions['width'],
                'height' => $dimensions['height'],
                'cod' => $order->payment_type == 'cash_on_delivery' ? 1 : 0
            ]);

            if ($response->successful()) {
                $data = $response->json();
                if (isset($data['data']['available_courier_companies']) && !empty($data['data']['available_courier_companies'])) {
                    // Return the cheapest rate
                    $rates = collect($data['data']['available_courier_companies'])->pluck('rate')->toArray();
                    return min($rates);
                }
            }
        } catch (\Exception $e) {
            Log::error('Shiprocket Rate Calculation Error: ' . $e->getMessage());
        }

        return 150.00; // Fallback rate
    }

    /**
     * Format phone number for Shiprocket (must be 10 digits for India)
     *
     * @param string $phone
     * @return string
     */
    private function formatPhoneNumber(string $phone): string
    {
        // Remove all non-numeric characters
        $phone = preg_replace('/[^0-9]/', '', $phone);

        // If phone starts with +91 or 91, remove it
        if (strlen($phone) > 10) {
            if (substr($phone, 0, 3) === '91') {
                $phone = substr($phone, 2);
            } elseif (substr($phone, 0, 2) === '91') {
                $phone = substr($phone, 2);
            }
        }

        // Ensure it's exactly 10 digits for India
        if (strlen($phone) === 10) {
            return $phone;
        }

        // If still not 10 digits, pad or truncate
        if (strlen($phone) < 10) {
            return str_pad($phone, 10, '0', STR_PAD_LEFT);
        } else {
            return substr($phone, -10); // Take last 10 digits
        }
    }

    /**
     * Create a shipping order with Shiprocket
     *
     * @param Order $order
     * @param array $fromAddress
     * @param array $toAddress
     * @return array
     */
    public function createShippingOrder(Order $order, array $fromAddress, array $toAddress): array
    {
        $token = $this->getAuthToken();
        if (!$token) {
            return [
                'success' => false,
                'message' => 'Authentication failed with Shiprocket. Please check your email and password.'
            ];
        }

        // Ensure pickup location exists in Shiprocket
        $pickupLocation = $this->getOrCreatePickupLocation($fromAddress);
        if (!$pickupLocation) {
            return [
                'success' => false,
                'message' => 'Failed to create pickup location in Shiprocket. Please check your fulfillment address.'
            ];
        }

        $orderItems = [];
        $totalWeight = 0;
        $totalLength = 0;
        $totalWidth = 0;
        $totalHeight = 0;

        foreach ($order->orderDetails as $orderDetail) {
            $product = $orderDetail->product;
            $weight = ($product->weight ?? 0.5) * $orderDetail->quantity;
            $totalWeight += $weight;
            $totalLength += ($product->length ?? 10);
            $totalWidth += ($product->width ?? 10);
            $totalHeight += ($product->height ?? 5);

            $orderItems[] = [
                'name' => $product->getTranslation('name'),
                'sku' => $product->stocks->first()->sku ?? 'SKU-' . $product->id,
                'units' => (int)$orderDetail->quantity,
                'selling_price' => (float)($orderDetail->price / $orderDetail->quantity),
                'discount' => 0,
                'tax' => (float)($orderDetail->tax / $orderDetail->quantity),
                'hsn' => $product->hsn_code ?? ''
            ];
        }

        // Split customer name into first and last name
        $customerName = $order->user->name;
        $nameParts = explode(' ', $customerName, 2);
        $firstName = $nameParts[0];
        $lastName = isset($nameParts[1]) ? $nameParts[1] : '';

        $data = [
            'order_id' => $order->code,
            'order_date' => date('Y-m-d H:i', $order->date),
            'pickup_location' => $pickupLocation,
            'channel_id' => '',
            'comment' => 'Order from Cloud Mart',
            'billing_customer_name' => $firstName,
            'billing_last_name' => $lastName,
            'billing_address' => $toAddress['address'],
            'billing_address_2' => '',
            'billing_city' => $toAddress['city'],
            'billing_pincode' => $toAddress['postal_code'],
            'billing_state' => $toAddress['state'],
            'billing_country' => $toAddress['country'],
            'billing_email' => $order->user->email,
            'billing_phone' => $this->formatPhoneNumber($toAddress['phone']),
            'shipping_is_billing' => true,
            'shipping_customer_name' => '',
            'shipping_last_name' => '',
            'shipping_address' => '',
            'shipping_address_2' => '',
            'shipping_city' => '',
            'shipping_pincode' => '',
            'shipping_country' => '',
            'shipping_state' => '',
            'shipping_email' => '',
            'shipping_phone' => '',
            'order_items' => $orderItems,
            'payment_method' => $order->payment_type == 'cash_on_delivery' ? 'COD' : 'Prepaid',
            'shipping_charges' => 0,
            'giftwrap_charges' => 0,
            'transaction_charges' => 0,
            'total_discount' => 0,
            'sub_total' => (float)$order->orderDetails->sum('price'),
            'length' => (float)max($totalLength, 1), // Minimum 1 cm
            'breadth' => (float)max($totalWidth, 1), // Minimum 1 cm
            'height' => (float)max($totalHeight, 1), // Minimum 1 cm
            'weight' => (float)max($totalWeight, 0.1) // Minimum 0.1 kg
        ];

        try {
            Log::info('Shiprocket Order Creation Request', [
                'url' => $this->baseUrl . '/orders/create/adhoc',
                'data' => $data
            ]);

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $token,
                'Content-Type' => 'application/json'
            ])->post($this->baseUrl . '/orders/create/adhoc', $data);

            Log::info('Shiprocket Order Creation Response', [
                'status' => $response->status(),
                'body' => $response->body()
            ]);

            if ($response->successful()) {
                $responseData = $response->json();

                if (isset($responseData['order_id'])) {
                    return [
                        'success' => true,
                        'tracking_id' => $responseData['shipment_id'] ?? null,
                        'shipment_id' => $responseData['order_id'],
                        'response' => json_encode($responseData)
                    ];
                }
            }

            Log::error('Shiprocket Order Creation Failed: ' . $response->body());
            return [
                'success' => false,
                'message' => 'Failed to create order with Shiprocket: ' . $response->body()
            ];

        } catch (\Exception $e) {
            Log::error('Shiprocket Order Creation Error: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error creating order with Shiprocket: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Track a shipment with Shiprocket
     *
     * @param string $trackingId
     * @return array
     */
    public function trackShipment(string $trackingId): array
    {
        $token = $this->getAuthToken();
        if (!$token) {
            return [
                'tracking_id' => $trackingId,
                'status' => 'unknown',
                'message' => 'Authentication failed'
            ];
        }

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $token,
                'Content-Type' => 'application/json'
            ])->get($this->baseUrl . '/courier/track/shipment/' . $trackingId);

            if ($response->successful()) {
                $data = $response->json();

                if (isset($data['tracking_data'])) {
                    $trackingData = $data['tracking_data'];

                    return [
                        'tracking_id' => $trackingId,
                        'status' => $trackingData['track_status'] ?? 'unknown',
                        'current_location' => $trackingData['current_location'] ?? 'Unknown',
                        'expected_delivery' => $trackingData['edd'] ?? null,
                        'tracking_url' => 'https://shiprocket.co/tracking/' . $trackingId,
                        'tracking_data' => $trackingData
                    ];
                }
            }

            return [
                'tracking_id' => $trackingId,
                'status' => 'unknown',
                'message' => 'No tracking data found'
            ];

        } catch (\Exception $e) {
            Log::error('Shiprocket Tracking Error: ' . $e->getMessage());
            return [
                'tracking_id' => $trackingId,
                'status' => 'error',
                'message' => 'Error tracking shipment: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get available courier companies for a route
     *
     * @param array $fromAddress
     * @param array $toAddress
     * @param float $weight
     * @param array $dimensions
     * @param bool $cod
     * @return array
     */
    public function getAvailableCouriers(array $fromAddress, array $toAddress, float $weight = 1.0, array $dimensions = [], bool $cod = false): array
    {
        $token = $this->getAuthToken();
        if (!$token) {
            return [];
        }

        $defaultDimensions = [
            'length' => 10,
            'width' => 10,
            'height' => 5
        ];

        $dimensions = array_merge($defaultDimensions, $dimensions);

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $token,
                'Content-Type' => 'application/json'
            ])->get($this->baseUrl . '/courier/serviceability', [
                'pickup_postcode' => $fromAddress['postal_code'],
                'delivery_postcode' => $toAddress['postal_code'],
                'weight' => $weight,
                'length' => $dimensions['length'],
                'breadth' => $dimensions['width'],
                'height' => $dimensions['height'],
                'cod' => $cod ? 1 : 0
            ]);

            if ($response->successful()) {
                $data = $response->json();
                if (isset($data['data']['available_courier_companies'])) {
                    return $data['data']['available_courier_companies'];
                }
            }

            Log::error('Shiprocket Get Couriers Failed: ' . $response->body());
        } catch (\Exception $e) {
            Log::error('Shiprocket Get Couriers Error: ' . $e->getMessage());
        }

        return [];
    }

    /**
     * Get pickup locations from Shiprocket
     *
     * @return array
     */
    public function getPickupLocations(): array
    {
        $token = $this->getAuthToken();
        if (!$token) {
            return [];
        }

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $token,
                'Content-Type' => 'application/json'
            ])->get($this->baseUrl . '/settings/company/pickup');

            if ($response->successful()) {
                $data = $response->json();
                if (isset($data['data']['shipping_address'])) {
                    return $data['data']['shipping_address'];
                }
            }

            Log::error('Shiprocket Get Pickup Locations Failed: ' . $response->body());
        } catch (\Exception $e) {
            Log::error('Shiprocket Get Pickup Locations Error: ' . $e->getMessage());
        }

        return [];
    }

    /**
     * Test authentication with Shiprocket
     *
     * @return array
     */
    public function testAuthentication(): array
    {
        try {
            $token = $this->getAuthToken();

            if ($token) {
                return [
                    'success' => true,
                    'message' => 'Authentication successful',
                    'token_length' => strlen($token)
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Authentication failed. Please check your email and password.'
                ];
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Authentication error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Test pickup location creation
     *
     * @param array $address
     * @return array
     */
    public function testPickupLocation(array $address): array
    {
        try {
            $pickupLocation = $this->getOrCreatePickupLocation($address);

            if ($pickupLocation) {
                return [
                    'success' => true,
                    'message' => 'Pickup location created/found successfully',
                    'pickup_location' => $pickupLocation
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Failed to create pickup location. Check logs for details.'
                ];
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Pickup location error: ' . $e->getMessage()
            ];
        }
    }
}
