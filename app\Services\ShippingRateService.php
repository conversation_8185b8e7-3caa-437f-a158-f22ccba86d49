<?php

namespace App\Services;

use App\Models\Order;
use App\Models\Product;
use App\Models\ShippingPartner;
use App\Models\ShippingConfiguration;
use App\Services\Shipping\ShippingServiceFactory;

class ShippingRateService
{
    /**
     * Calculate shipping rate for an order
     *
     * @param Order $order
     * @return float
     */
    public function calculateShippingRate(Order $order): float
    {
        // Get active shipping partner
        $shippingPartner = ShippingPartner::where('is_active', 1)->first();
        
        if (!$shippingPartner) {
            // If no shipping partner is active, use the default shipping cost
            return $order->shipping_cost;
        }
        
        // Get shipping configuration
        $configuration = ShippingConfiguration::where('shipping_partner_id', $shippingPartner->id)->first();
        
        if (!$configuration || !$configuration->use_shipping_rate_calculator) {
            // If shipping rate calculator is not enabled, use the default shipping cost
            return $order->shipping_cost;
        }
        
        try {
            // Get shipping service
            $shippingService = ShippingServiceFactory::getService($shippingPartner);
            
            // Get shipping addresses
            $fromAddresses = [];
            $toAddress = json_decode($order->shipping_address, true);
            
            // Get from addresses for each product
            foreach ($order->orderDetails as $orderDetail) {
                $product = $orderDetail->product;
                
                // Get from address based on product fulfillment type
                if (!isset($fromAddresses[$product->id])) {
                    if ($product->fulfillment_type == 'cloudmart') {
                        // Get Cloud Mart's fulfillment center address
                        if ($configuration && $configuration->cloudmart_fulfillment_address) {
                            $fromAddresses[$product->id] = json_decode($configuration->cloudmart_fulfillment_address, true);
                        } else {
                            // Default Cloud Mart address if not configured
                            $fromAddresses[$product->id] = [
                                'name' => 'Cloud Mart Fulfillment Center',
                                'address' => 'Cloud Mart Address',
                                'city' => 'Cloud Mart City',
                                'state' => 'Cloud Mart State',
                                'country' => 'Cloud Mart Country',
                                'postal_code' => 'Cloud Mart Postal Code',
                                'phone' => 'Cloud Mart Phone'
                            ];
                        }
                    } else {
                        // Get vendor's address with failsafe fallback to CloudMart address
                        $seller = $product->user;
                        $shop = \App\Models\Shop::where('user_id', $seller->id)->first();

                        // Try to get vendor's default address first
                        $vendorAddress = \App\Models\Address::where('user_id', $seller->id)
                            ->where('set_default', 1)
                            ->first();

                        if (!$vendorAddress) {
                            $vendorAddress = \App\Models\Address::where('user_id', $seller->id)->first();
                        }

                        // Check if vendor has a valid address
                        $hasValidVendorAddress = $vendorAddress &&
                            !empty($vendorAddress->address) &&
                            !empty($vendorAddress->postal_code) &&
                            !empty($vendorAddress->phone);

                        if ($hasValidVendorAddress) {
                            $fromAddresses[$product->id] = [
                                'name' => $shop ? $shop->name : $seller->name,
                                'address' => $vendorAddress->address,
                                'city' => $vendorAddress->city->name ?? 'Bengaluru',
                                'state' => $vendorAddress->state->name ?? 'Karnataka',
                                'country' => $vendorAddress->country->name ?? 'India',
                                'postal_code' => $vendorAddress->postal_code,
                                'phone' => $vendorAddress->phone
                            ];
                        } else {
                            // Failsafe: Use CloudMart fulfillment address as default for vendor products too
                            $config = \App\Models\ShippingConfiguration::whereHas('shippingPartner', function($query) {
                                $query->where('is_active', 1);
                            })->first();

                            if ($config && $config->cloudmart_fulfillment_address) {
                                $fromAddresses[$product->id] = json_decode($config->cloudmart_fulfillment_address, true);
                            } else {
                                // Ultimate fallback - hardcoded CloudMart address
                                $fromAddresses[$product->id] = [
                                    'name' => 'CloudMart Fulfillment Center',
                                    'address' => '5th Main, Koramangala',
                                    'city' => 'Bengaluru',
                                    'state' => 'Karnataka',
                                    'country' => 'India',
                                    'postal_code' => '560034',
                                    'phone' => '7483376552'
                                ];
                            }
                        }
                    }
                }
            }
            
            // For simplicity, use the first from address (in a real implementation, you might need to handle multiple from addresses)
            $fromAddress = reset($fromAddresses);
            
            // Calculate shipping rate
            $shippingRate = $shippingService->calculateRate($order, $fromAddress, $toAddress);
            
            return $shippingRate;
        } catch (\Exception $e) {
            // If there's an error, use the default shipping cost
            return $order->shipping_cost;
        }
    }
}
