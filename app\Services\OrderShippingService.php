<?php

namespace App\Services;

use App\Models\Order;
use App\Models\ShippingPartner;
use App\Models\ShippingConfiguration;
use App\Services\Shipping\ShippingServiceFactory;
use Illuminate\Support\Facades\Log;

class OrderShippingService
{
    /**
     * Create shipping order for an order with the best available shipping partner
     *
     * @param Order $order
     * @return array
     */
    public function createShippingOrder(Order $order): array
    {
        Log::info('Starting shipping order creation', [
            'order_id' => $order->id,
            'order_code' => $order->code
        ]);

        // Get active shipping partners ordered by priority (you can add priority field later)
        $activePartners = ShippingPartner::where('is_active', 1)
            ->whereHas('configurations', function($query) {
                $query->where('is_active', 1);
            })
            ->get();

        if ($activePartners->isEmpty()) {
            Log::warning('No active shipping partners available', [
                'order_id' => $order->id
            ]);
            return [
                'success' => false,
                'message' => 'No active shipping partners available'
            ];
        }

        $toAddress = json_decode($order->shipping_address, true);
        if (!$toAddress) {
            Log::error('Invalid shipping address for order', [
                'order_id' => $order->id,
                'shipping_address' => $order->shipping_address
            ]);
            return [
                'success' => false,
                'message' => 'Invalid shipping address'
            ];
        }

        $fromAddresses = $this->getFromAddresses($order);

        // Try each shipping partner until one succeeds
        foreach ($activePartners as $partner) {
            try {
                $shippingService = ShippingServiceFactory::getService($partner);
                
                // For simplicity, use the first from address
                $fromAddress = reset($fromAddresses);
                
                $result = $shippingService->createShippingOrder($order, $fromAddress, $toAddress);
                
                if ($result['success']) {
                    // Update order with shipping partner details
                    $order->shipping_partner_id = $partner->id;
                    $order->tracking_id = $result['tracking_id'];
                    $order->shipping_partner_response = $result['response'];
                    $order->save();
                    
                    Log::info("Shipping order created successfully with {$partner->name} for order {$order->code}");
                    
                    return [
                        'success' => true,
                        'partner' => $partner->name,
                        'tracking_id' => $result['tracking_id'],
                        'message' => "Shipping order created successfully with {$partner->name}"
                    ];
                }
                
                Log::warning("Failed to create shipping order with {$partner->name}: " . ($result['message'] ?? 'Unknown error'));
                
            } catch (\Exception $e) {
                Log::error("Error creating shipping order with {$partner->name}: " . $e->getMessage());
                continue;
            }
        }

        return [
            'success' => false,
            'message' => 'Failed to create shipping order with any available partner'
        ];
    }

    /**
     * Get shipping rates from all active partners for comparison
     *
     * @param Order $order
     * @return array
     */
    public function getShippingRates(Order $order): array
    {
        $activePartners = ShippingPartner::where('is_active', 1)
            ->whereHas('configurations', function($query) {
                $query->where('is_active', 1)
                      ->where('use_shipping_rate_calculator', 1);
            })
            ->get();

        $rates = [];
        $toAddress = json_decode($order->shipping_address, true);
        $fromAddresses = $this->getFromAddresses($order);

        foreach ($activePartners as $partner) {
            try {
                $shippingService = ShippingServiceFactory::getService($partner);
                $fromAddress = reset($fromAddresses);
                
                $rate = $shippingService->calculateRate($order, $fromAddress, $toAddress);
                
                $rates[] = [
                    'partner_id' => $partner->id,
                    'partner_name' => $partner->name,
                    'rate' => $rate
                ];
                
            } catch (\Exception $e) {
                Log::error("Error calculating rate for {$partner->name}: " . $e->getMessage());
            }
        }

        // Sort by rate (cheapest first)
        usort($rates, function($a, $b) {
            return $a['rate'] <=> $b['rate'];
        });

        return $rates;
    }

    /**
     * Get from addresses based on order details and fulfillment types
     *
     * @param Order $order
     * @return array
     */
    protected function getFromAddresses(Order $order): array
    {
        $fromAddresses = [];

        foreach ($order->orderDetails as $orderDetail) {
            $product = $orderDetail->product;

            if (!isset($fromAddresses[$product->id])) {
                if ($product->fulfillment_type == 'cloudmart') {
                    // Get Cloud Mart's fulfillment center address
                    $config = ShippingConfiguration::whereHas('shippingPartner', function($query) {
                        $query->where('is_active', 1);
                    })->first();

                    if ($config && $config->cloudmart_fulfillment_address) {
                        $fromAddresses[$product->id] = json_decode($config->cloudmart_fulfillment_address, true);
                    } else {
                        // Default Cloud Mart address
                        $fromAddresses[$product->id] = [
                            'name' => 'Cloud Mart Fulfillment Center',
                            'address' => 'Cloud Mart Address',
                            'city' => 'Cloud Mart City',
                            'state' => 'Cloud Mart State',
                            'country' => 'India',
                            'postal_code' => '110001',
                            'phone' => '9999999999'
                        ];
                    }
                } else {
                    // Get vendor's address
                    $seller = $product->user;
                    $shop = \App\Models\Shop::where('user_id', $seller->id)->first();

                    // Try to get vendor's default address first, fallback to user table fields
                    $vendorAddress = \App\Models\Address::where('user_id', $seller->id)
                        ->where('set_default', 1)
                        ->first();

                    if (!$vendorAddress) {
                        $vendorAddress = \App\Models\Address::where('user_id', $seller->id)->first();
                    }

                    if ($vendorAddress) {
                        $fromAddresses[$product->id] = [
                            'name' => $shop ? $shop->name : $seller->name,
                            'address' => $vendorAddress->address,
                            'city' => $vendorAddress->city->name ?? 'Unknown City',
                            'state' => $vendorAddress->state->name ?? 'Unknown State',
                            'country' => $vendorAddress->country->name ?? 'India',
                            'postal_code' => $vendorAddress->postal_code,
                            'phone' => $vendorAddress->phone
                        ];
                    } else {
                        // Fallback to user table fields if no address found
                        $fromAddresses[$product->id] = [
                            'name' => $shop ? $shop->name : $seller->name,
                            'address' => $seller->address ?? 'Vendor Address Not Set',
                            'city' => $seller->city ?? 'Vendor City Not Set',
                            'state' => $seller->state ?? 'Vendor State Not Set',
                            'country' => $seller->country ?? 'India',
                            'postal_code' => $seller->postal_code ?? '110001',
                            'phone' => $seller->phone ?? '9999999999'
                        ];
                    }
                }
            }
        }

        return $fromAddresses;
    }

    /**
     * Track shipment for an order
     *
     * @param Order $order
     * @return array
     */
    public function trackShipment(Order $order): array
    {
        if (!$order->shipping_partner_id || !$order->tracking_id) {
            return [
                'success' => false,
                'message' => 'No shipping partner or tracking ID found for this order'
            ];
        }

        try {
            $partner = ShippingPartner::find($order->shipping_partner_id);
            if (!$partner) {
                return [
                    'success' => false,
                    'message' => 'Shipping partner not found'
                ];
            }

            $shippingService = ShippingServiceFactory::getService($partner);
            $trackingData = $shippingService->trackShipment($order->tracking_id);

            return [
                'success' => true,
                'tracking_data' => $trackingData
            ];

        } catch (\Exception $e) {
            Log::error("Error tracking shipment for order {$order->code}: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error tracking shipment: ' . $e->getMessage()
            ];
        }
    }
}
